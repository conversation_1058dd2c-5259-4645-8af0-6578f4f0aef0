'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import styled from 'styled-components';
import {
  ChevronLeft,
  Award,
  Calendar,
  CheckCircle,
  Clock,
  Plus,
  Trash2,
  User,
  Building,
  Briefcase,
  MessageSquare,
  Edit,
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import useUserStore from '@/store/userStore';
import { toast } from 'react-hot-toast';

import DeleteTaskModal from './components/DeleteTaskModal';
import AddRequestModal from './components/AddRequestModal';
// @ts-ignore - Component exists but TypeScript may not recognize it yet
import EditProgressModal from './components/EditProgressModal';
// @ts-ignore - Component exists but TypeScript may not recognize it yet
import DeleteProgressModal from './components/DeleteProgressModal';
import { appTheme, appStyles } from '@/app/theme';
import TaskChatButton from '@/components/TaskChatButton';

// Types based on the Prisma schema
interface TaskStatus {
  id: number;
  name: string;
  color: string;
  description?: string;
}

interface User {
  id: number;
  name: string;
  email: string;
  departmentName?: string;
  organizationName?: string;
  isLeader?: boolean;
  isAdmin?: boolean;
  isOwner?: boolean;
}

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  organization?: Organization;
}

interface ProgressType {
  id: number;
  name: string;
  description?: string;
  displayName?: string;
  color?: string;
}

interface TaskProgress {
  id: number;
  taskId: number;
  updatedByUserId: number;
  progressTypeId: number;
  progressDescription: string;
  createdAt: Date;
  updatedAt: Date;
  updatedByUser: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    userRole?: {
      id: number;
      name: string;
      description?: string;
      isOwner: boolean;
      isAdmin: boolean;
      isMember: boolean;
      createdAt: Date;
      updatedAt: Date;
    };
  };
  progressType?: {
    id: number;
    name: string;
    description?: string;
    displayName?: string;
    color?: string;
  };
}

interface TaskAssignment {
  id: number;
  taskId: number;
  userId: number;
  assignedAt: Date;
  assignedBy?: number;
  isLeader: boolean;
  isActive: boolean;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    departmentName?: string;
    organizationName?: string;
    isLeader?: boolean;
    isAdmin?: boolean;
    isOwner?: boolean;
  };
}

interface Task {
  id: number;
  taskTitle: string;
  taskDescription?: string;
  createdByUserId: number;
  organizationId?: number;
  departmentId?: number;
  organization?: Organization;
  department?: Department;
  taskAssignments: TaskAssignment[];
  statusId: number;
  points?: number;
  isClaimPoint?: boolean;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  createdByUser: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    departmentName?: string;
    organizationName?: string;
    isLeader?: boolean;
  };
  status: TaskStatus;
  taskProgresses?: TaskProgress[];
}

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: ${appTheme.spacing.xl};
  gap: 0;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: ${appTheme.borderRadius.lg};

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    padding: ${appTheme.spacing.lg};
    gap: ${appTheme.spacing.sm};
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
    gap: ${appTheme.spacing.sm};
    border: none;
    border-radius: 0;
    background: transparent;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.sm};
  }
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  font-size: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 44px; /* Touch-friendly */

  &:hover {
    background-color: #f5f5f5;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.sm};
    font-size: 0.9rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  }
`;

const Title = styled.h1`
  font-size: ${appTheme.typography.fontSizes['3xl']};
  font-weight: ${appTheme.typography.fontWeights.bold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
  flex-wrap: wrap;

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    font-size: ${appTheme.typography.fontSizes['2xl']};
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: ${appTheme.typography.fontSizes.xl};
    gap: ${appTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: ${appTheme.typography.fontSizes.lg};
    flex-direction: column;
    align-items: flex-start;
  }
`;

const StatusBadge = styled.span<{ color: string }>`
  background-color: ${props => `${props.color}20`};
  color: ${props => props.color};
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  margin-left: 1rem;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.8rem;
    padding: 0.2rem 0.6rem;
    margin-left: 0.5rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    margin-left: 0;
    margin-top: 0.25rem;
  }
`;

const PointsBadge = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #fef3c7;
  color: #92400e;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  & svg {
    color: #f59e0b;
  }

  & span {
    line-height: 1;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.65rem;
    padding: 2px 6px;
    gap: 3px;
  }
`;

const RewardBadge = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #e0f2fe;
  color: #0369a1;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  & svg {
    color: #0ea5e9;
  }

  & span {
    line-height: 1;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.65rem;
    padding: 2px 6px;
    gap: 3px;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${appTheme.spacing.xs};

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: ${appTheme.spacing.xs};
  }
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  cursor: pointer;
  transition: all 0.2s;
  min-height: 44px; /* Touch-friendly */
  min-width: 44px;

  &:hover {
    background-color: ${appTheme.colors.background.lighter};
  }

  &.edit {
    color: ${appTheme.colors.primary};
  }

  &.delete {
    color: ${appTheme.colors.error};
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 40px;
    height: 40px;
  }
`;

const Card = styled.div`
  background: white;
  border-radius: ${appTheme.borderRadius.lg};
  padding: 1rem;
  box-shadow: ${appTheme.shadows.sm};
  margin-bottom: 1rem;

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    padding: 0.875rem;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border-radius: ${appTheme.borderRadius.md};
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: 0.625rem;
    margin-bottom: 0.5rem;
  }
`;

const MainLayout = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1rem;

  /* Tablet - maintain two columns but adjust ratio */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    grid-template-columns: 1.5fr 1fr;
    gap: 0.875rem;
  }

  /* Mobile - single column */
  @media (max-width: ${appTheme.breakpoints.md}) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: 0.5rem;
  }
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    gap: 0.875rem;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: 0.75rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: 0.5rem;
  }
`;

const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    gap: 0.875rem;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: 0.75rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: 0.5rem;
  }
`;

const SidebarCard = styled.div`
  background: white;
  border-radius: ${appTheme.borderRadius.lg};
  padding: 1rem;
  box-shadow: ${appTheme.shadows.sm};
  border: 1px solid ${appTheme.colors.border};

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    padding: 0.875rem;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 0.75rem;
    border-radius: ${appTheme.borderRadius.md};
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: 0.625rem;
  }
`;

const MainContentCard = styled.div`
  background: white;
  border-radius: ${appTheme.borderRadius.lg};
  padding: 1rem;
  box-shadow: ${appTheme.shadows.sm};
  border: 1px solid ${appTheme.colors.border};

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    padding: 0.875rem;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 0.75rem;
    border-radius: ${appTheme.borderRadius.md};
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: 0.625rem;
  }
`;

const Description = styled.div`
  margin: 1.5rem 0;
  color: #555;
  line-height: 1.6;
  max-height: 800px;
  overflow-y: auto;
  padding-right: 0.5rem;

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    margin: 1.25rem 0;
    max-height: 600px;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    margin: 1rem 0;
    max-height: 400px;
    padding-right: 0.25rem;
    font-size: 0.9rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    margin: 0.75rem 0;
    max-height: 300px;
    font-size: 0.875rem;
  }

  /* Style HTML content from rich text editor */
  p {
    margin: 0.5rem 0;
    line-height: 1.6;
  }

  p:first-child {
    margin-top: 0;
  }

  p:last-child {
    margin-bottom: 0;
  }

  strong,
  b {
    font-weight: 600;
    color: #333;
  }

  em,
  i {
    font-style: italic;
  }

  ul,
  ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
  }

  li {
    margin: 0.25rem 0;
    line-height: 1.5;
  }

  a {
    color: #3b82f6;
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
  }

  /* Handle alignment */
  .text-left {
    text-align: left;
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }

  .text-justify {
    text-align: justify;
  }
`;

const MetaGroupTitle = styled.h4`
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f3f4f6;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.7rem;
    margin-bottom: 0.375rem;
    padding-bottom: 0.375rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 0.65rem;
  }
`;

const MetaItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem 0;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: 0.5rem;
    padding: 0.375rem 0;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: 0.375rem;
    padding: 0.25rem 0;
  }
`;

const MetaLabel = styled.span`
  font-size: 0.6rem;
  color: #888;
  display: block;
  margin-bottom: 0.15rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.55rem;
    margin-bottom: 0.1rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 0.5rem;
  }
`;

const MetaValue = styled.span`
  font-weight: 500;
  color: #333;
  display: block;
  font-size: 0.8rem;
  line-height: 1.2;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.75rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 0.7rem;
  }
`;

const UserDepartmentOrg = styled.div`
  font-size: 0.7rem;
  color: #9ca3af;
  font-weight: 400;
  font-style: italic;
  margin-top: 0.1rem;
`;

const LeaderBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background-color: #fbbf24;
  color: #92400e;
  font-size: 0.65rem;
  font-weight: 600;
  border-radius: 4px;
  margin-left: 6px;
`;

const AdminBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background-color: #3b82f6;
  color: white;
  font-size: 0.65rem;
  font-weight: 600;
  border-radius: 4px;
  margin-left: 6px;
`;

const OwnerBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background-color: #dc2626;
  color: white;
  font-size: 0.65rem;
  font-weight: 600;
  border-radius: 4px;
  margin-left: 6px;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #f44336;
  text-align: center;

  h2 {
    margin-bottom: 1rem;
  }
`;

const ProgressSection = styled.div`
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    margin-top: 1.75rem;
    padding-top: 1.25rem;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    margin-top: 1.5rem;
    padding-top: 1rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    margin-top: 1rem;
    padding-top: 0.75rem;
  }
`;

const ProgressTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 16px;
    background-color: ${appTheme.colors.primary};
    border-radius: 2px;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    gap: 0.375rem;

    &::before {
      width: 2px;
      height: 14px;
    }
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }
`;

const ProgressList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: 0.5rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: 0.375rem;
  }
`;

const ProgressItem = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: 8px;
  padding: 1rem;
  position: relative;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

  &:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
  }

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    padding: 0.875rem;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 0.75rem;
    border-radius: 6px;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: 0.625rem;
  }
`;

const ProgressHeader = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: 0.75rem;
    margin-bottom: 0.5rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: 0.5rem;
    margin-bottom: 0.375rem;
  }
`;

const ProgressUserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const ProgressMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
`;

const UserAvatar = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${appTheme.colors.background.lighter};
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: ${appTheme.colors.text.secondary};
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid white;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
`;

const ProgressUser = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  font-size: 0.875rem;
`;

const ProgressType = styled.span<{ $bgColor?: string; $textColor?: string }>`
  display: inline-flex;
  align-items: center;
  padding: 0.15rem 0.5rem;
  background-color: ${props =>
    props.$bgColor ? `${props.$bgColor}20` : `${appTheme.colors.primary}10`};
  color: ${props => props.$textColor || appTheme.colors.primary};
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
`;

const ProgressActions = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: 0.25rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: 0.125rem;
  }
`;

const ProgressDate = styled.div`
  color: ${appTheme.colors.text.secondary};
  font-size: 0.625rem; /* 10px */
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ProgressActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.secondary};
  padding: 0.25rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  min-width: 32px;
  min-height: 32px;

  &:hover {
    background-color: ${appTheme.colors.background.lighter};
    color: ${props => props.color || appTheme.colors.primary};
  }

  &.edit:hover {
    color: ${appTheme.colors.primary};
  }

  &.delete:hover {
    color: ${appTheme.colors.error};
  }

  /* Mobile - larger touch targets */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-width: 40px;
    min-height: 40px;
    padding: 0.375rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    min-width: 44px;
    min-height: 44px;
    padding: 0.5rem;
  }
`;

const ProgressContent = styled.div`
  color: ${appTheme.colors.text.primary};
  line-height: 1.5;
  white-space: pre-wrap;
  font-size: 0.875rem;
  padding-top: 0.25rem;
  padding-left: 3rem;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: ${appTheme.colors.border};
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.8rem;
    padding-left: 2.5rem;

    &::before {
      left: 1.25rem;
    }
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 0.75rem;
    padding-left: 2rem;

    &::before {
      left: 1rem;
    }
  }
`;

const EmptyProgress = styled.div`
  text-align: center;
  padding: 1.5rem;
  color: ${appTheme.colors.text.secondary};
  background-color: ${appTheme.colors.background.lighter};
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;

  & svg {
    color: ${appTheme.colors.text.secondary}80;
  }

  & p {
    font-size: 0.875rem;
    margin: 0;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 1.25rem;
    gap: 0.5rem;

    & p {
      font-size: 0.8rem;
    }
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: 1rem;
    gap: 0.375rem;

    & p {
      font-size: 0.75rem;
    }
  }
`;

const CommentInput = styled.div`
  display: flex;
  gap: ${appTheme.spacing.md};
  margin-top: ${appTheme.spacing.xl};
  padding-top: ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    gap: ${appTheme.spacing.sm};
    margin-top: ${appTheme.spacing.lg};
    padding-top: ${appTheme.spacing.md};
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    flex-direction: column;
    gap: ${appTheme.spacing.sm};
    margin-top: ${appTheme.spacing.md};
    padding-top: ${appTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: ${appTheme.spacing.xs};
  }
`;

const CommentTextArea = styled.textarea`
  ${appStyles.input}
  flex: 1;
  min-height: 44px;
  resize: none;
  font-family: inherit;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-height: 48px; /* Larger touch target */
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  }
`;

const CommentButton = styled.button`
  ${appStyles.button.primary}
  height: 44px;
  padding: 0 ${appTheme.spacing.lg};
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.md};

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    height: 48px; /* Larger touch target */
    width: 100%; /* Full width on mobile */
    justify-content: center;
    padding: 0 ${appTheme.spacing.md};
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: 0 ${appTheme.spacing.sm};
    font-size: 0.875rem;
  }
`;

// Header responsive components
const HeaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: ${appTheme.spacing.md};

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${appTheme.spacing.sm};
  }
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  flex-wrap: wrap;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 100%;
  }
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  flex-wrap: wrap;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 100%;
    justify-content: space-between;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${appTheme.spacing.xs};
  }
`;

const BadgeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  flex-wrap: wrap;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: ${appTheme.spacing.xs};
  }
`;

// Section title component
const SectionTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 20px;
    background-color: ${appTheme.colors.primary};
    border-radius: 2px;
  }

  /* Tablet */
  @media (max-width: ${appTheme.breakpoints.lg}) {
    font-size: 1.125rem;
    margin-bottom: 0.875rem;

    &::before {
      height: 18px;
    }
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    gap: 0.375rem;

    &::before {
      width: 2px;
      height: 16px;
    }
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;

    &::before {
      height: 14px;
    }
  }
`;

// Progress filter components
const FilterContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  align-items: center;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
`;

const FilterLabel = styled.span`
  font-size: 0.875rem;
  color: ${appTheme.colors.text.secondary};

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.8rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 0.75rem;
  }
`;

const FilterOptions = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: 0.375rem;
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: 0.25rem;
  }
`;

const FilterOption = styled.label<{ $isSelected: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: ${appTheme.borderRadius.md};
  background-color: ${props => props.$isSelected ? appTheme.colors.primary : '#f3f4f6'};
  color: ${props => props.$isSelected ? 'white' : appTheme.colors.text.secondary};
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px; /* Touch-friendly */

  input {
    display: none;
  }

  /* Mobile */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    min-height: 36px; /* Larger touch target */
  }

  /* Small mobile */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    min-height: 40px; /* Even larger for small screens */
  }
`;

export default function TaskDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { getToken } = useAuth();
  const { userData } = useUserStore();
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAddRequestModalOpen, setIsAddRequestModalOpen] = useState(false);
  const [isEditProgressModalOpen, setIsEditProgressModalOpen] = useState(false);
  const [isDeleteProgressModalOpen, setIsDeleteProgressModalOpen] = useState(false);
  const [progressTypes, setProgressTypes] = useState<Array<ProgressType>>([]);
  const [selectedProgressTypeId, setSelectedProgressTypeId] = useState<number | null>(null);
  const [selectedProgress, setSelectedProgress] = useState<TaskProgress | null>(null);
  const [commentText, setCommentText] = useState('');

  // Check if user has Boss role
  const isBoss = userData?.role.isOwner;

  // Fetch task progress types
  useEffect(() => {
    const fetchProgressTypes = async () => {
      try {
        const token = await getToken();
        const response = await fetch('/api/v1/task-progress-types', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        if (!response.ok) {
          throw new Error(`Failed to fetch progress types: ${response.statusText}`);
        }
        const data = await response.json();
        setProgressTypes(data.progressTypes);
      } catch (err) {
        console.error('Error fetching progress types:', err);
        toast.error('Failed to load progress types');
      }
    };

    fetchProgressTypes();
  }, [getToken]);

  // Fetch task progresses with optional type filter
  const fetchTaskProgresses = async (token?: string) => {
    try {
      if (!token) {
        token = await getToken();
      }

      let url = `/api/v1/task-progress?taskId=${id}`;
      if (selectedProgressTypeId !== null) {
        url += `&progressTypeId=${selectedProgressTypeId}`;
      }

      const progressResponse = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!progressResponse.ok) {
        console.warn(`Failed to fetch task progresses: ${progressResponse.statusText}`);
        return;
      }

      const progressData = await progressResponse.json();
      if (progressData.taskProgresses && progressData.taskProgresses.length > 0) {
        // Update task object with the latest progresses
        setTask(prevTask => {
          if (!prevTask) return prevTask;
          return {
            ...prevTask,
            taskProgresses: progressData.taskProgresses,
          };
        });
      } else {
        // Update task object with empty progresses
        setTask(prevTask => {
          if (!prevTask) return prevTask;
          return {
            ...prevTask,
            taskProgresses: [],
          };
        });
      }
    } catch (err) {
      console.error('Error fetching task progresses:', err);
      toast.error('Failed to load task progresses');
    }
  };

  // Handle progress type filter change
  const handleProgressTypeFilterChange = (typeId: number | null) => {
    setSelectedProgressTypeId(typeId);
  };

  // Watch for changes to selectedProgressTypeId and fetch filtered progresses
  useEffect(() => {
    if (id) {
      fetchTaskProgresses();
    }
  }, [selectedProgressTypeId, id]);

  // Fetch department and organization info for users
  const fetchUserDepartmentInfo = async (taskData: any, token: string) => {
    try {
      // Get all unique user IDs from task assignments and created by user
      const userIds = new Set<number>();

      // Add assigned users
      taskData.taskAssignments?.forEach((assignment: any) => {
        userIds.add(assignment.user.id);
      });

      // Add created by user
      if (taskData.createdByUser?.id) {
        userIds.add(taskData.createdByUser.id);
      }

      if (userIds.size === 0) return;

      // Fetch member info from the organization using task assignment users API
      const memberResponse = await fetch(
        `/api/v1/task-assignment-users?organizationId=${taskData.organizationId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (memberResponse.ok) {
        const memberData = await memberResponse.json();
        const members = memberData.members || [];

        // Create a map of user ID to member info
        const userMemberMap = new Map();
        members.forEach((member: any) => {
          userMemberMap.set(member.user.id, {
            departmentName: member.department?.name,
            organizationName: member.department?.organization?.name,
            isLeader: member.isLeader,
            isAdmin: member.isAdmin,
            isOwner: member.isOwner,
          });
        });

        // Update task with enhanced user info
        const updatedTask = {
          ...taskData,
          taskAssignments: taskData.taskAssignments?.map((assignment: any) => ({
            ...assignment,
            user: {
              ...assignment.user,
              ...userMemberMap.get(assignment.user.id),
            },
          })),
          createdByUser: {
            ...taskData.createdByUser,
            ...userMemberMap.get(taskData.createdByUser?.id),
          },
        };

        setTask(updatedTask);
      }
    } catch (err) {
      console.error('Error fetching user department info:', err);
    }
  };

  useEffect(() => {
    const fetchTaskDetails = async () => {
      try {
        setLoading(true);
        const token = await getToken();

        const response = await fetch(`/api/v1/task?id=${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch task details: ${response.statusText}`);
        }

        const data = await response.json();

        // Fetch task progresses separately
        fetchTaskProgresses(token);
        setTask(data.task);

        // Fetch department and organization info for users
        if (token) {
          await fetchUserDepartmentInfo(data.task, token);
        }
      } catch (err) {
        console.error('Error fetching task details:', err);
        setError(err instanceof Error ? err.message : 'Failed to load task details');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchTaskDetails();
    }
  }, [id, getToken]);

  // Format date to display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Format date with time
  const formatDateTime = (date: Date) => {
    const formattedDate = new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });

    const formattedTime = new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });

    return `${formattedDate}, ${formattedTime}`;
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleEdit = () => {
    router.push(`/kanban/${id}/edit`);
  };

  const handleAddRequest = () => {
    setIsAddRequestModalOpen(true);
  };

  const handleAddComment = async () => {
    if (!commentText.trim()) {
      toast.error('Please enter a comment');
      return;
    }

    try {
      const token = await getToken();

      const response = await fetch('/api/v1/task-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          taskId: Number(id),
          progressDescription: commentText,
          progressTypeId: 1, // Fixed to comment type (ID: 1)
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to add comment: ${response.statusText}`);
      }

      const data = await response.json();
      handleProgressAdded(data.taskProgress);

      toast.success('Comment added successfully');
      setCommentText('');
    } catch (err) {
      console.error('Error adding comment:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to add comment');
    }
  };

  const handleProgressAdded = (newProgress: TaskProgress) => {
    // Update the task with the new progress
    if (task) {
      const updatedTask = {
        ...task,
        taskProgresses: task.taskProgresses ? [newProgress, ...task.taskProgresses] : [newProgress],
      };
      setTask(updatedTask);
    }
  };

  const handleEditProgress = (progress: TaskProgress) => {
    setSelectedProgress(progress);
    setIsEditProgressModalOpen(true);
  };

  const handleProgressUpdated = (updatedProgress: TaskProgress) => {
    // Update the task with the updated progress
    if (task && task.taskProgresses) {
      const updatedTask = {
        ...task,
        taskProgresses: task.taskProgresses.map(p =>
          p.id === updatedProgress.id ? updatedProgress : p
        ),
      };
      setTask(updatedTask);
    }
  };

  const handleDeleteProgress = (progress: TaskProgress) => {
    setSelectedProgress(progress);
    setIsDeleteProgressModalOpen(true);
  };

  const handleProgressDeleted = (deletedProgressId: number) => {
    // Update the task with the deleted progress
    if (task && task.taskProgresses) {
      const updatedTask = {
        ...task,
        taskProgresses: task.taskProgresses.filter(p => p.id !== deletedProgressId),
      };
      setTask(updatedTask);
    }
  };

  const handleDelete = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteSuccess = () => {
    toast.success('Task deleted successfully');
    router.push('/kanban');
  };

  if (loading) {
    return (
      <Container>
        <Card>
          {/* Skeleton for header */}
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
              <div className="h-8 w-64 bg-gray-200 rounded ml-4"></div>
              <div className="h-6 w-24 bg-gray-200 rounded-full ml-4"></div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div className="h-6 w-16 bg-gray-200 rounded-full"></div>
              <div className="h-8 w-16 bg-gray-200 rounded"></div>
            </div>
          </div>

          {/* Skeleton for description */}
          <div className="mt-6 space-y-2">
            <div className="h-4 w-full bg-gray-200 rounded"></div>
            <div className="h-4 w-full bg-gray-200 rounded"></div>
            <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
          </div>

          {/* Skeleton for meta section */}
          <div className="mt-6 pt-6 border-t border-gray-100">
            <div className="flex flex-wrap gap-5">
              {/* People Group */}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '0.75rem',
                  flex: '1 0 220px',
                }}
              >
                <div className="h-3 w-16 bg-gray-200 rounded mb-2"></div>
                <div className="flex items-start gap-2 mb-3">
                  <div className="h-4 w-4 bg-gray-200 rounded-full mt-1"></div>
                  <div>
                    <div className="h-2 w-16 bg-gray-200 rounded mb-1"></div>
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="h-4 w-4 bg-gray-200 rounded-full mt-1"></div>
                  <div>
                    <div className="h-2 w-16 bg-gray-200 rounded mb-1"></div>
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>

              {/* Dates Group */}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '0.75rem',
                  flex: '1 0 220px',
                }}
              >
                <div className="h-3 w-16 bg-gray-200 rounded mb-2"></div>
                <div className="flex items-start gap-2 mb-3">
                  <div className="h-4 w-4 bg-gray-200 rounded-full mt-1"></div>
                  <div>
                    <div className="h-2 w-16 bg-gray-200 rounded mb-1"></div>
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="h-4 w-4 bg-gray-200 rounded-full mt-1"></div>
                  <div>
                    <div className="h-2 w-16 bg-gray-200 rounded mb-1"></div>
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>

              {/* Details Group */}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '0.75rem',
                  flex: '1 0 220px',
                }}
              >
                <div className="h-3 w-16 bg-gray-200 rounded mb-2"></div>
                <div className="flex items-start gap-2 mb-3">
                  <div className="h-4 w-4 bg-gray-200 rounded-full mt-1"></div>
                  <div>
                    <div className="h-2 w-16 bg-gray-200 rounded mb-1"></div>
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <div className="h-4 w-4 bg-gray-200 rounded-full mt-1"></div>
                  <div>
                    <div className="h-2 w-16 bg-gray-200 rounded mb-1"></div>
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Skeleton for progress section */}
          <div className="mt-6 pt-6 border-t border-gray-100">
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1rem',
              }}
            >
              <div className="h-5 w-32 bg-gray-200 rounded"></div>
              <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
            </div>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex gap-3 mb-3">
                    <div className="h-9 w-9 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-3 w-24 bg-gray-200 rounded mb-1"></div>
                      <div className="h-4 w-40 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                  <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </Container>
    );
  }

  if (error || !task) {
    return (
      <Container>
        <ErrorState>
          <h2>Error</h2>
          <p>{error || 'Task not found'}</p>
          <BackButton onClick={handleGoBack}>
            <ChevronLeft size={20} />
            Back to Kanban Board
          </BackButton>
        </ErrorState>
      </Container>
    );
  }

  return (
    <Container>
      {/* Header removed and layout reorganized */}

      {/* Delete Task Modal */}
      <DeleteTaskModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onSuccess={handleDeleteSuccess}
        task={task}
      />

      {/* Add Request Modal */}
      <AddRequestModal
        isOpen={isAddRequestModalOpen}
        onClose={() => setIsAddRequestModalOpen(false)}
        onProgressAdded={handleProgressAdded}
        taskId={task.id}
      />

      {/* Edit Progress Modal */}
      {selectedProgress && (
        <EditProgressModal
          isOpen={isEditProgressModalOpen}
          onClose={() => setIsEditProgressModalOpen(false)}
          onProgressUpdated={handleProgressUpdated}
          progress={selectedProgress}
        />
      )}

      {/* Delete Progress Modal */}
      {selectedProgress && (
        <DeleteProgressModal
          isOpen={isDeleteProgressModalOpen}
          onClose={() => setIsDeleteProgressModalOpen(false)}
          onSuccess={() => handleProgressDeleted(selectedProgress.id)}
          progress={selectedProgress}
        />
      )}

      {/* Header Section */}
      <Card>
        <HeaderContainer>
          <HeaderLeft>
            <BackButton onClick={handleGoBack}>
              <ChevronLeft size={20} />
            </BackButton>
            <Title>{task.taskTitle}</Title>
            <StatusBadge color={task.status.color}>{task.status.name}</StatusBadge>
          </HeaderLeft>
          <HeaderRight>
            <BadgeContainer>
              {task.points !== undefined && task.points > 0 && (
                <PointsBadge>
                  <Award size={12} />
                  <span>{task.points} pts</span>
                </PointsBadge>
              )}
              {task.isClaimPoint && (
                <RewardBadge>
                  <CheckCircle size={12} />
                  <span>Claimable</span>
                </RewardBadge>
              )}

              {/* Chat Button - Available to all users */}
              <TaskChatButton
                taskId={task.id}
                taskTitle={task.taskTitle}
                variant="outline"
                size="sm"
              />
            </BadgeContainer>

            {isBoss && (
              <ActionButtons>
                <ActionButton
                  className="edit"
                  onClick={handleEdit}
                  aria-label="Edit Task"
                >
                  <Edit size={16} strokeWidth={1.5} />
                </ActionButton>
                <ActionButton className="delete" onClick={handleDelete} aria-label="Delete Task">
                  <Trash2 size={16} color="#ef4444" />
                </ActionButton>
              </ActionButtons>
            )}
          </HeaderRight>
        </HeaderContainer>
      </Card>

      {/* Main Layout with Two Columns */}
      <MainLayout>
        {/* Main Content Area (Left Side) */}
        <MainContent>
          {/* Task Description */}
          {task.taskDescription && (
            <MainContentCard>
              <SectionTitle>Description</SectionTitle>
              <Description dangerouslySetInnerHTML={{ __html: task.taskDescription }} />
            </MainContentCard>
          )}

          {/* Task Progress Section */}
          <MainContentCard>
            <ProgressSection style={{ margin: 0, paddingTop: 0, borderTop: 'none' }}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '1rem',
                }}
              >
                <ProgressTitle>Task Progress</ProgressTitle>
                <ActionButton
                  onClick={handleAddRequest}
                  className="edit"
                  aria-label="Create Request"
                  style={{
                    backgroundColor: `${appTheme.colors.primary}10`,
                    width: 'auto',
                    padding: '0 12px',
                    borderRadius: '4px',
                    gap: '6px',
                  }}
                >
                  <Plus size={16} />
                  <span style={{ fontSize: '0.875rem', fontWeight: 500 }}>Create Request</span>
                </ActionButton>
              </div>

              {/* Progress Type Filter */}
              <FilterContainer>
                <FilterLabel>Filter by type:</FilterLabel>
                <FilterOptions>
                  <FilterOption $isSelected={selectedProgressTypeId === null}>
                    <input
                      type="radio"
                      name="progressTypeFilter"
                      value="all"
                      checked={selectedProgressTypeId === null}
                      onChange={() => handleProgressTypeFilterChange(null)}
                    />
                    All
                  </FilterOption>

                  {progressTypes.map(type => (
                    <FilterOption key={type.id} $isSelected={selectedProgressTypeId === type.id}>
                      <input
                        type="radio"
                        name="progressTypeFilter"
                        value={type.id}
                        checked={selectedProgressTypeId === type.id}
                        onChange={() => handleProgressTypeFilterChange(type.id)}
                      />
                      {type.displayName || type.name}
                    </FilterOption>
                  ))}
                </FilterOptions>
              </FilterContainer>

              {/* Comment Input Section */}
              <CommentInput style={{ marginBottom: '1.5rem' }}>
                <CommentTextArea
                  placeholder="Add a comment..."
                  value={commentText}
                  onChange={e => setCommentText(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleAddComment();
                    }
                  }}
                />
                <CommentButton onClick={handleAddComment}>
                  <MessageSquare size={16} />
                  Comment
                </CommentButton>
              </CommentInput>

              {task.taskProgresses && task.taskProgresses.length > 0 ? (
                <ProgressList>
                  {task.taskProgresses.map(progress => (
                    <ProgressItem key={progress.id}>
                      <ProgressHeader>
                        <UserAvatar>
                          {progress.updatedByUser.imageUrl ? (
                            <img
                              src={progress.updatedByUser.imageUrl}
                              alt={`${progress.updatedByUser.firstName} ${progress.updatedByUser.lastName}`}
                            />
                          ) : (
                            `${progress.updatedByUser.firstName.charAt(0)}${progress.updatedByUser.lastName.charAt(0)}`
                          )}
                        </UserAvatar>

                        <ProgressMeta>
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'start',
                            }}
                          >
                            <ProgressUserInfo>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                <ProgressUser>
                                  {progress.updatedByUser.firstName}{' '}
                                  {progress.updatedByUser.lastName}
                                </ProgressUser>
                                <span
                                  style={{
                                    fontSize: '0.65rem',
                                    padding: '0.1rem 0.4rem',
                                    borderRadius: '10px',
                                    backgroundColor: '#f0f9ff',
                                    color: '#0369a1',
                                    fontWeight: '500',
                                  }}
                                >
                                  {progress.updatedByUser.userRole
                                    ? progress.updatedByUser.userRole.name
                                    : progress.updatedByUser.id === task.createdByUserId
                                      ? 'Creator'
                                      : 'Member'}
                                </span>
                              </div>

                              <ProgressDate>
                                {progress.progressType && (
                                  <ProgressType
                                    $bgColor={progress.progressType.color}
                                    $textColor={progress.progressType.color}
                                  >
                                    {progress.progressType.displayName ||
                                      progress.progressType.name}
                                  </ProgressType>
                                )}
                                <Clock size={10} />
                                {formatDateTime(progress.createdAt)}
                              </ProgressDate>
                            </ProgressUserInfo>

                            {progress.updatedByUserId === userData?.id &&
                              progress.progressTypeId !== 3 && (
                                <ProgressActions>
                                  <ProgressActionButton
                                    onClick={() => handleEditProgress(progress)}
                                    className="edit"
                                    aria-label="Edit progress"
                                  >
                                    <Edit size={14} />
                                  </ProgressActionButton>
                                  <ProgressActionButton
                                    onClick={() => handleDeleteProgress(progress)}
                                    className="delete"
                                    aria-label="Delete progress"
                                  >
                                    <Trash2 size={14} />
                                  </ProgressActionButton>
                                </ProgressActions>
                              )}
                          </div>
                        </ProgressMeta>
                      </ProgressHeader>
                      <ProgressContent>{progress.progressDescription}</ProgressContent>
                    </ProgressItem>
                  ))}
                </ProgressList>
              ) : (
                <EmptyProgress>
                  <MessageSquare size={20} />
                  <p>No progress updates yet. Click "Create Request" to create the first update.</p>
                </EmptyProgress>
              )}
            </ProgressSection>
          </MainContentCard>
        </MainContent>

        {/* Sidebar (Right Side) */}
        <Sidebar>
          {/* People Section */}
          <SidebarCard>
            <MetaGroupTitle>People</MetaGroupTitle>
            <MetaItem>
              <User size={16} color="#888" style={{ marginTop: '2px' }} />
              <div style={{ width: '100%' }}>
                <MetaLabel>CREATED BY</MetaLabel>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    marginTop: '0.5rem',
                  }}
                >
                  <div
                    style={{
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      backgroundColor: '#f3f4f6',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontWeight: 600,
                      fontSize: '0.65rem',
                      color: '#6b7280',
                      overflow: 'hidden',
                      flexShrink: 0,
                      border: '2px solid #10b981',
                      boxShadow: '0 0 0 1px rgba(16, 185, 129, 0.1)',
                    }}
                  >
                    {task.createdByUser.imageUrl ? (
                      <img
                        src={task.createdByUser.imageUrl}
                        alt={`${task.createdByUser.firstName} ${task.createdByUser.lastName}`}
                        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                      />
                    ) : (
                      `${task.createdByUser.firstName.charAt(0)}${task.createdByUser.lastName.charAt(0)}`
                    )}
                  </div>
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div
                      style={{
                        fontWeight: 500,
                        color: '#333',
                        fontSize: '0.8rem',
                        lineHeight: 1.2,
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      {task.createdByUser.firstName} {task.createdByUser.lastName}
                      {task.createdByUser.isLeader && <LeaderBadge>Leader</LeaderBadge>}
                    </div>
                    {task.createdByUser.email && (
                      <div
                        style={{
                          fontSize: '0.7rem',
                          color: '#888',
                          marginTop: '0.1rem',
                        }}
                      >
                        {task.createdByUser.email}
                      </div>
                    )}
                    {(task.createdByUser.departmentName || task.createdByUser.organizationName) && (
                      <UserDepartmentOrg>
                        {task.createdByUser.departmentName && task.createdByUser.organizationName
                          ? `${task.createdByUser.departmentName} • ${task.createdByUser.organizationName}`
                          : task.createdByUser.departmentName ||
                            task.createdByUser.organizationName}
                      </UserDepartmentOrg>
                    )}
                  </div>
                </div>
              </div>
            </MetaItem>

            <MetaItem>
              <User size={16} color="#888" style={{ marginTop: '2px' }} />
              <div style={{ width: '100%' }}>
                <MetaLabel>ASSIGNED TO</MetaLabel>
                {task.taskAssignments && task.taskAssignments.length > 0 ? (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '0.75rem',
                      marginTop: '0.5rem',
                    }}
                  >
                    {/* Leaders Section */}
                    {task.taskAssignments.filter(assignment => assignment.isLeader).length > 0 && (
                      <div>
                        <div
                          style={{
                            fontSize: '0.65rem',
                            fontWeight: 600,
                            color: '#0ea5e9',
                            marginBottom: '0.5rem',
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px',
                          }}
                        >
                          Leaders
                        </div>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                          {task.taskAssignments
                            .filter(assignment => assignment.isLeader)
                            .map(assignment => (
                              <div
                                key={assignment.id}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '0.5rem',
                                  backgroundColor: '#f8fafc',
                                  border: '1px solid #e2e8f0',
                                  borderRadius: '8px',
                                  padding: '0.5rem',
                                }}
                              >
                                <div
                                  style={{
                                    width: '20px',
                                    height: '20px',
                                    borderRadius: '50%',
                                    backgroundColor: '#f3f4f6',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontWeight: 600,
                                    fontSize: '0.6rem',
                                    color: '#6b7280',
                                    overflow: 'hidden',
                                    flexShrink: 0,
                                    border: '2px solid #0ea5e9',
                                    boxShadow: '0 0 0 1px rgba(14, 165, 233, 0.1)',
                                  }}
                                >
                                  {assignment.user.imageUrl ? (
                                    <img
                                      src={assignment.user.imageUrl}
                                      alt={`${assignment.user.firstName} ${assignment.user.lastName}`}
                                      style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                    />
                                  ) : (
                                    `${assignment.user.firstName.charAt(0)}${assignment.user.lastName.charAt(0)}`
                                  )}
                                </div>
                                <div style={{ flex: 1, minWidth: 0 }}>
                                  <div
                                    style={{
                                      fontWeight: 500,
                                      color: '#333',
                                      fontSize: '0.75rem',
                                      lineHeight: 1.2,
                                      display: 'flex',
                                      alignItems: 'center',
                                    }}
                                  >
                                    {assignment.user.firstName} {assignment.user.lastName}
                                    {assignment.isLeader && (
                                      <LeaderBadge
                                        style={{
                                          fontSize: '0.6rem',
                                          padding: '1px 4px',
                                          marginLeft: '4px',
                                        }}
                                      >
                                        Leader
                                      </LeaderBadge>
                                    )}
                                  </div>
                                  {assignment.user.email && (
                                    <div
                                      style={{
                                        fontSize: '0.65rem',
                                        color: '#888',
                                        marginTop: '0.1rem',
                                      }}
                                    >
                                      {assignment.user.email}
                                    </div>
                                  )}
                                  {(assignment.user.departmentName ||
                                    assignment.user.organizationName) && (
                                    <UserDepartmentOrg style={{ fontSize: '0.6rem' }}>
                                      {assignment.user.departmentName &&
                                      assignment.user.organizationName
                                        ? `${assignment.user.departmentName} • ${assignment.user.organizationName}`
                                        : assignment.user.departmentName ||
                                          assignment.user.organizationName}
                                    </UserDepartmentOrg>
                                  )}
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>
                    )}

                    {/* Members Section */}
                    {task.taskAssignments.filter(assignment => !assignment.isLeader).length > 0 && (
                      <div>
                        <div
                          style={{
                            fontSize: '0.65rem',
                            fontWeight: 600,
                            color: '#6b7280',
                            marginBottom: '0.5rem',
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px',
                          }}
                        >
                          Users
                        </div>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                          {task.taskAssignments
                            .filter(assignment => !assignment.isLeader)
                            .map(assignment => (
                              <div
                                key={assignment.id}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '0.5rem',
                                  backgroundColor: '#f8fafc',
                                  border: '1px solid #e2e8f0',
                                  borderRadius: '8px',
                                  padding: '0.5rem',
                                }}
                              >
                                <div
                                  style={{
                                    width: '20px',
                                    height: '20px',
                                    borderRadius: '50%',
                                    backgroundColor: '#f3f4f6',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontWeight: 600,
                                    fontSize: '0.6rem',
                                    color: '#6b7280',
                                    overflow: 'hidden',
                                    flexShrink: 0,
                                    border: '2px solid white',
                                    boxShadow: '0 0 0 1px rgba(0, 0, 0, 0.05)',
                                  }}
                                >
                                  {assignment.user.imageUrl ? (
                                    <img
                                      src={assignment.user.imageUrl}
                                      alt={`${assignment.user.firstName} ${assignment.user.lastName}`}
                                      style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                    />
                                  ) : (
                                    `${assignment.user.firstName.charAt(0)}${assignment.user.lastName.charAt(0)}`
                                  )}
                                </div>
                                <div style={{ flex: 1, minWidth: 0 }}>
                                  <div
                                    style={{
                                      fontWeight: 500,
                                      color: '#333',
                                      fontSize: '0.75rem',
                                      lineHeight: 1.2,
                                      display: 'flex',
                                      alignItems: 'center',
                                    }}
                                  >
                                    {assignment.user.firstName} {assignment.user.lastName}
                                    {assignment.user.isOwner && (
                                      <OwnerBadge
                                        style={{
                                          fontSize: '0.6rem',
                                          padding: '1px 4px',
                                          marginLeft: '4px',
                                        }}
                                      >
                                        Owner
                                      </OwnerBadge>
                                    )}
                                    {assignment.user.isAdmin && (
                                      <AdminBadge
                                        style={{
                                          fontSize: '0.6rem',
                                          padding: '1px 4px',
                                          marginLeft: '4px',
                                        }}
                                      >
                                        Admin
                                      </AdminBadge>
                                    )}
                                    {assignment.user.isLeader && (
                                      <LeaderBadge
                                        style={{
                                          fontSize: '0.6rem',
                                          padding: '1px 4px',
                                          marginLeft: '4px',
                                        }}
                                      >
                                        Leader
                                      </LeaderBadge>
                                    )}
                                  </div>
                                  {assignment.user.email && (
                                    <div
                                      style={{
                                        fontSize: '0.65rem',
                                        color: '#888',
                                        marginTop: '0.1rem',
                                      }}
                                    >
                                      {assignment.user.email}
                                    </div>
                                  )}
                                  {(assignment.user.departmentName ||
                                    assignment.user.organizationName) && (
                                    <UserDepartmentOrg style={{ fontSize: '0.6rem' }}>
                                      {assignment.user.departmentName &&
                                      assignment.user.organizationName
                                        ? `${assignment.user.departmentName} • ${assignment.user.organizationName}`
                                        : assignment.user.departmentName ||
                                          assignment.user.organizationName}
                                    </UserDepartmentOrg>
                                  )}
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <MetaValue>No assignees</MetaValue>
                )}
              </div>
            </MetaItem>
          </SidebarCard>

          {/* Date Details Section */}
          <SidebarCard>
            <MetaGroupTitle>Date Details</MetaGroupTitle>
            {task.dueDate && (
              <MetaItem>
                <Calendar size={16} color="#888" style={{ marginTop: '2px' }} />
                <div>
                  <MetaLabel>DUE DATE</MetaLabel>
                  <MetaValue>{formatDate(task.dueDate)}</MetaValue>
                </div>
              </MetaItem>
            )}

            <MetaItem>
              <Clock size={16} color="#888" style={{ marginTop: '2px' }} />
              <div>
                <MetaLabel>CREATED</MetaLabel>
                <MetaValue>{formatDate(task.createdAt)}</MetaValue>
              </div>
            </MetaItem>

            <MetaItem>
              <Clock size={16} color="#888" style={{ marginTop: '2px' }} />
              <div>
                <MetaLabel>UPDATED</MetaLabel>
                <MetaValue>{formatDate(task.updatedAt)}</MetaValue>
              </div>
            </MetaItem>

            {/* Additional Details */}
            {(task.points ||
              task.isClaimPoint !== undefined ||
              task.organization ||
              task.department) && (
              <>
                <div
                  style={{
                    borderTop: '1px solid #e2e8f0',
                    margin: '1rem 0 0.5rem 0',
                    paddingTop: '1rem',
                  }}
                >
                  <MetaGroupTitle style={{ marginBottom: '0.5rem' }}>
                    Additional Details
                  </MetaGroupTitle>
                </div>

                {task.points && (
                  <MetaItem>
                    <Award size={16} color="#888" style={{ marginTop: '2px' }} />
                    <div>
                      <MetaLabel>POINTS</MetaLabel>
                      <MetaValue>{task.points} pts</MetaValue>
                    </div>
                  </MetaItem>
                )}

                {task.isClaimPoint !== undefined && (
                  <MetaItem>
                    <CheckCircle
                      size={16}
                      color={task.isClaimPoint ? '#0ea5e9' : '#888'}
                      style={{ marginTop: '2px' }}
                    />
                    <div>
                      <MetaLabel>CLAIMABLE</MetaLabel>
                      <MetaValue>{task.isClaimPoint ? 'Yes' : 'No'}</MetaValue>
                    </div>
                  </MetaItem>
                )}

                {task.organization && (
                  <MetaItem>
                    <Building size={16} color="#888" style={{ marginTop: '2px' }} />
                    <div>
                      <MetaLabel>ORGANIZATION</MetaLabel>
                      <MetaValue>{task.organization.name}</MetaValue>
                    </div>
                  </MetaItem>
                )}

                {task.department && (
                  <MetaItem>
                    <Briefcase size={16} color="#888" style={{ marginTop: '2px' }} />
                    <div>
                      <MetaLabel>DEPARTMENT</MetaLabel>
                      <MetaValue>{task.department.name}</MetaValue>
                    </div>
                  </MetaItem>
                )}
              </>
            )}
          </SidebarCard>
        </Sidebar>
      </MainLayout>
    </Container>
  );
}
